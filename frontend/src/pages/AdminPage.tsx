import { useState } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import { Layout } from '../components';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { FiUsers, FiDatabase, FiActivity, FiTrash2, FiMessageSquare } from 'react-icons/fi';

// 导入管理员组件
import { UserManagement, BuoyManagement, DataManagement, SystemMonitor, FeedbackManagement } from '../components/admin';

// 管理员页面导航项类型
interface AdminNavItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  path: string;
}

const AdminPage = () => {
  const { user } = useAuth();
  const { theme } = useTheme();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<string>(
    location.pathname.split('/').pop() || 'users'
  );

  // 管理员导航项
  const adminNavItems: AdminNavItem[] = [
    {
      id: 'users',
      title: '用户管理',
      icon: <FiUsers />,
      path: '/admin/users',
    },
    {
      id: 'buoys',
      title: '浮标管理',
      icon: <FiDatabase />,
      path: '/admin/buoys',
    },
    {
      id: 'data',
      title: '数据管理',
      icon: <FiTrash2 />,
      path: '/admin/data',
    },
    {
      id: 'feedback',
      title: '用户反馈',
      icon: <FiMessageSquare />,
      path: '/admin/feedback',
    },
    {
      id: 'monitor',
      title: '系统监控',
      icon: <FiActivity />,
      path: '/admin/monitor',
    },
  ];

  // 用户信息显示
  const userInfo = (
    <span className="hidden sm:inline-block mr-4 text-secondary">
      {user?.username} ({user?.role === 'admin' ? '管理员' : '用户'})
    </span>
  );

  return (
    <Layout
      withContainer
      title="管理系统"
      actions={userInfo}
    >
      {/* 管理员导航标签 */}
      <div className="mb-6 border-b border-theme">
        <nav className="flex -mb-px">
          {adminNavItems.map((item) => (
            <Link
              key={item.id}
              to={item.path}
              className={`
                flex items-center py-4 px-6 border-b-2 font-medium text-sm
                ${activeTab === item.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-secondary hover:text-primary hover:border-secondary'}
              `}
              onClick={() => setActiveTab(item.id)}
            >
              {item.icon}
              <span className="ml-2">{item.title}</span>
            </Link>
          ))}
        </nav>
      </div>

      {/* 内容区域 */}
      <div className="bg-main shadow rounded-lg p-6">
        <Routes>
          <Route path="users" element={<UserManagement />} />
          <Route path="buoys" element={<BuoyManagement />} />
          <Route path="data" element={<DataManagement />} />
          <Route path="feedback" element={<FeedbackManagement />} />
          <Route path="monitor" element={<SystemMonitor />} />
          <Route path="*" element={<UserManagement />} />
        </Routes>
      </div>
    </Layout>
  );
};

export default AdminPage;
